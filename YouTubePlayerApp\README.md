# YouTube Desktop Player

A Windows desktop application that embeds YouTube.com in a native application window using WebView2.

## Features

- **Native Windows Experience**: Built with WPF and WebView2 for optimal performance
- **Full YouTube Functionality**: Complete access to YouTube features including video playback, playlists, and user accounts
- **Browser-like Navigation**: Back, forward, refresh, and home navigation controls
- **Fullscreen Support**: Press F11 or use the menu to toggle fullscreen mode
- **Zoom Controls**: Zoom in/out and reset zoom functionality
- **Settings Panel**: Configurable options for startup behavior, video quality, and privacy
- **Keyboard Shortcuts**: F11 for fullscreen, F5 for refresh

## Requirements

- Windows 10 version 1903+ or Windows 11
- .NET 8.0 Runtime
- WebView2 Runtime (usually pre-installed on modern Windows)

## Technology Stack

- **.NET 8 WPF**: Native Windows UI framework
- **Microsoft WebView2**: Chromium-based web engine for excellent YouTube compatibility
- **C#**: Primary development language

## Building and Running

### Prerequisites

1. Install [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
2. Ensure WebView2 Runtime is installed (check Windows Update)

### Build Commands

```bash
# Restore dependencies
dotnet restore

# Build the application
dotnet build

# Run the application
dotnet run

# Build for release
dotnet build --configuration Release

# Publish as single file executable
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output ./publish
```

## Project Structure

```
YouTubePlayerApp/
├── MainWindow.xaml          # Main application window UI
├── MainWindow.xaml.cs       # Main window logic and WebView2 integration
├── SettingsWindow.xaml      # Settings dialog UI
├── SettingsWindow.xaml.cs   # Settings dialog logic
├── AppSettings.cs           # Application settings management
├── App.xaml                 # Application-level resources
├── App.xaml.cs              # Application startup logic
└── YouTubePlayerApp.csproj  # Project configuration
```

## Configuration

Settings are automatically saved to:
`%APPDATA%\YouTubePlayerApp\settings.json`

Available settings include:
- Startup behavior (start with Windows, fullscreen, window position)
- Home page preferences (YouTube home, trending, or custom URL)
- Video quality defaults
- Privacy options (cookie management, tracking)

## Key Features Implementation

### WebView2 Integration
- Uses Microsoft's WebView2 control for modern web compatibility
- Custom user agent string for optimal YouTube experience
- Proper audio/video codec support through Chromium engine

### Navigation Controls
- Browser-style back/forward navigation
- Refresh and home page functionality
- Fullscreen toggle with F11 key support

### Settings Framework
- Extensible settings system using JSON serialization
- Organized into General, Video, and Privacy categories
- Persistent storage in user's AppData folder

## Future Enhancement Opportunities

1. **Picture-in-Picture Mode**: Floating mini-player window
2. **Custom Themes**: Dark/light mode toggle
3. **Download Integration**: Video download capabilities
4. **Playlist Management**: Local playlist creation and management
5. **Keyboard Shortcuts**: Custom hotkey configuration
6. **System Tray Integration**: Minimize to system tray functionality

## Troubleshooting

### WebView2 Issues
If the application fails to load YouTube:
1. Ensure WebView2 Runtime is installed
2. Check Windows Update for the latest version
3. Clear application data through Settings > Privacy > Clear All Data

### Performance Issues
- Reduce zoom level if videos lag
- Close other browser instances to free up resources
- Check video quality settings in the Settings panel

## Development Notes

This application demonstrates:
- Modern Windows desktop development with WPF
- WebView2 integration for web content embedding
- Settings management and persistence
- Clean MVVM-friendly architecture for future enhancements

The codebase is structured for easy extension and maintenance, with clear separation between UI, business logic, and configuration management.
