using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace YouTubePlayerApp;

/// <summary>
/// Comprehensive ad blocker for YouTube with multiple blocking methods
/// </summary>
public class AdBlocker
{
    private readonly List<string> _blockedDomains;
    private readonly List<Regex> _urlPatterns;
    private readonly Dictionary<string, int> _blockedStats;
    private CoreWebView2? _webView;

    public AdBlocker()
    {
        _blockedDomains = LoadBlockedDomains();
        _urlPatterns = LoadUrlPatterns();
        _blockedStats = new Dictionary<string, int>();
    }

    public void Initialize(CoreWebView2 webView)
    {
        _webView = webView;
        
        if (AppSettings.BlockAds)
        {
            EnableAdBlocking();
        }
    }

    public void EnableAdBlocking()
    {
        if (_webView == null) return;

        // 1. Request filtering - block ad domains
        _webView.WebResourceRequested += OnWebResourceRequested;
        _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

        // 2. DOM content loaded - inject CSS and JavaScript
        _webView.DOMContentLoaded += OnDOMContentLoaded;

        // 3. Navigation completed - apply blocking
        _webView.NavigationCompleted += OnNavigationCompleted;
    }

    public void DisableAdBlocking()
    {
        if (_webView == null) return;

        _webView.WebResourceRequested -= OnWebResourceRequested;
        _webView.DOMContentLoaded -= OnDOMContentLoaded;
        _webView.NavigationCompleted -= OnNavigationCompleted;
        _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
    }

    private async void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        var uri = e.Request.Uri;
        
        // Block known ad domains and patterns
        if (ShouldBlockRequest(uri))
        {
            e.Response = _webView?.Environment.CreateWebResourceResponse(
                null, 200, "OK", "");
            
            IncrementBlockedStat("Requests");
            
            if (AppSettings.ShowAdBlockStats)
            {
                System.Diagnostics.Debug.WriteLine($"Blocked: {uri}");
            }
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            // Inject CSS to hide ad elements
            await InjectAdBlockingCSS();
            
            // Inject JavaScript for dynamic ad removal
            await InjectAdBlockingJavaScript();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error injecting ad blocking code: {ex.Message}");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            // Additional cleanup after page load
            await RemoveAdsAfterLoad();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in post-load ad removal: {ex.Message}");
        }
    }

    private bool ShouldBlockRequest(string uri)
    {
        try
        {
            var uriObj = new Uri(uri);
            var host = uriObj.Host.ToLower();

            // Check against blocked domains
            if (_blockedDomains.Any(domain => host.Contains(domain)))
            {
                return true;
            }

            // Check against URL patterns
            if (_urlPatterns.Any(pattern => pattern.IsMatch(uri)))
            {
                return true;
            }

            // YouTube specific ad patterns
            if (IsYouTubeAdRequest(uri))
            {
                return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private bool IsYouTubeAdRequest(string uri)
    {
        var youtubeAdPatterns = new[]
        {
            "/pagead/",
            "/ptracking",
            "googleads",
            "googlesyndication",
            "googletagservices",
            "/ads?",
            "/ad?",
            "doubleclick.net",
            "/gen_204?",
            "/youtubei/v1/log_event",
            "/api/stats/",
            "video_ads",
            "/adview",
            "/ad_companion",
            "/get_midroll_info",
            "/videoplayback/.*[&?]gir=yes",
            "/api/timedtext.*[&?]caps=asr"
        };

        return youtubeAdPatterns.Any(pattern => 
            Regex.IsMatch(uri, pattern, RegexOptions.IgnoreCase));
    }

    private async Task InjectAdBlockingCSS()
    {
        if (_webView == null) return;

        var css = GetAdBlockingCSS();
        var script = $@"
            (function() {{
                var style = document.createElement('style');
                style.textContent = `{css}`;
                document.head.appendChild(style);
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task InjectAdBlockingJavaScript()
    {
        if (_webView == null) return;

        var script = GetAdBlockingJavaScript();
        await _webView.ExecuteScriptAsync(script);
    }

    private async Task RemoveAdsAfterLoad()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                // Remove video ads
                const adContainers = document.querySelectorAll([
                    '.video-ads',
                    '.ytp-ad-module',
                    '.ytp-ad-overlay-container',
                    '.ytp-ad-image-overlay',
                    '.ytp-ad-text-overlay',
                    '[class*=""ad-container""]',
                    '[id*=""ad-container""]'
                ].join(','));
                
                adContainers.forEach(el => el.remove());
                
                // Skip ad button auto-click
                const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                if (skipButton && skipButton.offsetParent !== null) {
                    skipButton.click();
                }
                
                // Remove banner ads
                const bannerAds = document.querySelectorAll([
                    '[data-ad-status]',
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer',
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer'
                ].join(','));
                
                bannerAds.forEach(el => el.remove());
                
                return adContainers.length + bannerAds.length;
            })();
        ";

        try
        {
            var result = await _webView.ExecuteScriptAsync(script);
            if (int.TryParse(result, out int blockedCount) && blockedCount > 0)
            {
                IncrementBlockedStat("Elements", blockedCount);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error executing ad removal script: {ex.Message}");
        }
    }

    private string GetAdBlockingCSS()
    {
        return @"
            /* Video Ad Containers */
            .video-ads,
            .ytp-ad-module,
            .ytp-ad-overlay-container,
            .ytp-ad-image-overlay,
            .ytp-ad-text-overlay,
            .ytp-ad-player-overlay,
            .ytp-ad-skip-button-container,
            .ytp-ad-preview-container,
            [class*='ad-container'],
            [id*='ad-container'] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Banner and Display Ads */
            .ytd-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer,
            .ytd-banner-promo-renderer,
            .ytd-in-feed-ad-layout-renderer,
            .ytd-ad-slot-renderer,
            [data-ad-status],
            [data-is-ad='true'],
            [aria-label*='Ad'],
            [aria-label*='Sponsored'] {
                display: none !important;
            }
            
            /* Sidebar Ads */
            #player-ads,
            #masthead-ad,
            .ytd-companion-slot-renderer,
            .ytd-action-companion-ad-renderer {
                display: none !important;
            }
            
            /* Hide ad-related overlays */
            .ytp-ad-overlay-container *,
            .ytp-ad-image-overlay *,
            .ytp-ad-text-overlay * {
                display: none !important;
            }
            
            /* Premium upgrade prompts */
            .ytd-popup-container[dialog][role='dialog'],
            ytd-mealbar-promo-renderer {
                display: none !important;
            }
        ";
    }

    private string GetAdBlockingJavaScript()
    {
        return @"
            (function() {
                'use strict';
                
                // Monitor for dynamically added ads
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                // Check if it's an ad element
                                if (node.matches && node.matches([
                                    '.video-ads',
                                    '.ytp-ad-module',
                                    '[class*=""ad-container""]',
                                    '[data-ad-status]',
                                    '.ytd-promoted-video-renderer'
                                ].join(','))) {
                                    node.remove();
                                }
                                
                                // Check children for ad elements
                                const adElements = node.querySelectorAll([
                                    '.video-ads',
                                    '.ytp-ad-module',
                                    '[class*=""ad-container""]',
                                    '[data-ad-status]',
                                    '.ytd-promoted-video-renderer'
                                ].join(','));
                                
                                adElements.forEach(el => el.remove());
                            }
                        });
                    });
                });
                
                // Start observing
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                // Override ad-related functions
                if (window.yt && window.yt.config_) {
                    window.yt.config_.EXPERIMENT_FLAGS = window.yt.config_.EXPERIMENT_FLAGS || {};
                    window.yt.config_.EXPERIMENT_FLAGS.web_player_ads_control_config = {enabled: false};
                }
                
                // Auto-skip ads
                setInterval(function() {
                    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                    if (skipButton && skipButton.offsetParent !== null) {
                        skipButton.click();
                    }
                }, 1000);
                
                // Block video ad loading
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && (
                        url.includes('/pagead/') ||
                        url.includes('googleads') ||
                        url.includes('doubleclick')
                    )) {
                        return Promise.reject(new Error('Blocked by ad blocker'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                console.log('YouTube Ad Blocker activated');
            })();
        ";
    }

    private List<string> LoadBlockedDomains()
    {
        return new List<string>
        {
            // Google Ads
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googletagservices.com",
            "doubleclick.net",
            "googleadservices.com",
            
            // YouTube specific
            "youtube.com/pagead",
            "youtube.com/ptracking",
            "youtube.com/api/stats",
            
            // Analytics and tracking
            "google-analytics.com",
            "googletagmanager.com",
            "adsystem.amazon.com",
            "facebook.com/tr",
            
            // Other ad networks
            "amazon-adsystem.com",
            "adsafeprotected.com",
            "moatads.com",
            "scorecardresearch.com",
            "outbrain.com",
            "taboola.com"
        };
    }

    private List<Regex> LoadUrlPatterns()
    {
        var patterns = new List<string>
        {
            @"/pagead/",
            @"/ads\?",
            @"/ad\?",
            @"[&?]ad_type=",
            @"[&?]adurl=",
            @"googleads\.g\.doubleclick\.net",
            @"googlesyndication\.com",
            @"youtube\.com/ptracking",
            @"youtube\.com/api/stats/",
            @"youtube\.com/youtubei/v1/log_event",
            @"[&?]gir=yes",
            @"/gen_204\?"
        };

        return patterns.Select(p => new Regex(p, RegexOptions.IgnoreCase | RegexOptions.Compiled)).ToList();
    }

    private void IncrementBlockedStat(string category, int count = 1)
    {
        if (_blockedStats.ContainsKey(category))
        {
            _blockedStats[category] += count;
        }
        else
        {
            _blockedStats[category] = count;
        }
    }

    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    public void ClearStats()
    {
        _blockedStats.Clear();
    }

    public int GetTotalBlocked()
    {
        return _blockedStats.Values.Sum();
    }
}
