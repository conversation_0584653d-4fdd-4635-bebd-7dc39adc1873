using System.Windows;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for SettingsWindow.xaml
/// </summary>
public partial class SettingsWindow : Window
{
    public SettingsWindow()
    {
        InitializeComponent();
        LoadSettings();
    }

    private void LoadSettings()
    {
        // Load settings from configuration
        // This is a placeholder - in a real application, you would load from a config file or registry
        StartWithWindowsCheckBox.IsChecked = AppSettings.StartWithWindows;
        StartFullScreenCheckBox.IsChecked = AppSettings.StartFullScreen;
        RememberLastPositionCheckBox.IsChecked = AppSettings.RememberLastPosition;
        
        VideoQualityComboBox.SelectedIndex = AppSettings.DefaultVideoQuality;
        AutoplayCheckBox.IsChecked = AppSettings.EnableAutoplay;
        MuteOnStartupCheckBox.IsChecked = AppSettings.MuteOnStartup;
        VolumeSlider.Value = AppSettings.DefaultVolume;
        
        ClearCookiesOnExitCheckBox.IsChecked = AppSettings.ClearCookiesOnExit;
        BlockThirdPartyCookiesCheckBox.IsChecked = AppSettings.BlockThirdPartyCookies;
        DoNotTrackCheckBox.IsChecked = AppSettings.DoNotTrack;
        BlockAdsCheckBox.IsChecked = AppSettings.BlockAds;
        
        // Set home page options
        switch (AppSettings.HomePage)
        {
            case "https://www.youtube.com":
                YouTubeHomeRadio.IsChecked = true;
                break;
            case "https://www.youtube.com/feed/trending":
                YouTubeTrendingRadio.IsChecked = true;
                break;
            default:
                CustomUrlRadio.IsChecked = true;
                CustomUrlTextBox.Text = AppSettings.HomePage;
                break;
        }
    }

    private void SaveSettings()
    {
        // Save settings to configuration
        AppSettings.StartWithWindows = StartWithWindowsCheckBox.IsChecked ?? false;
        AppSettings.StartFullScreen = StartFullScreenCheckBox.IsChecked ?? false;
        AppSettings.RememberLastPosition = RememberLastPositionCheckBox.IsChecked ?? false;
        
        AppSettings.DefaultVideoQuality = VideoQualityComboBox.SelectedIndex;
        AppSettings.EnableAutoplay = AutoplayCheckBox.IsChecked ?? true;
        AppSettings.MuteOnStartup = MuteOnStartupCheckBox.IsChecked ?? false;
        AppSettings.DefaultVolume = (int)VolumeSlider.Value;
        
        AppSettings.ClearCookiesOnExit = ClearCookiesOnExitCheckBox.IsChecked ?? false;
        AppSettings.BlockThirdPartyCookies = BlockThirdPartyCookiesCheckBox.IsChecked ?? false;
        AppSettings.DoNotTrack = DoNotTrackCheckBox.IsChecked ?? false;
        AppSettings.BlockAds = BlockAdsCheckBox.IsChecked ?? false;
        
        // Save home page setting
        if (YouTubeHomeRadio.IsChecked == true)
            AppSettings.HomePage = "https://www.youtube.com";
        else if (YouTubeTrendingRadio.IsChecked == true)
            AppSettings.HomePage = "https://www.youtube.com/feed/trending";
        else
            AppSettings.HomePage = CustomUrlTextBox.Text;
        
        AppSettings.Save();
    }

    private void OkButton_Click(object sender, RoutedEventArgs e)
    {
        SaveSettings();
        DialogResult = true;
        Close();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void ClearDataButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "This will clear all cookies, cache, and stored data. Are you sure?",
            "Clear All Data",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);
            
        if (result == MessageBoxResult.Yes)
        {
            // TODO: Implement data clearing functionality
            MessageBox.Show("Data cleared successfully.", "Clear Data", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
