{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"YouTubePlayerApp/1.0.0": {"dependencies": {"Microsoft.Web.WebView2": "1.0.3351.48", "Microsoft.Web.WebView2.Core": "1.0.3351.48", "Microsoft.Web.WebView2.WinForms": "1.0.3351.48", "Microsoft.Web.WebView2.Wpf": "1.0.3351.48"}, "runtime": {"YouTubePlayerApp.dll": {}}}, "Microsoft.Web.WebView2/1.0.3351.48": {"runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.3351.48"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.3351.48"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.3351.48"}}}, "Microsoft.Web.WebView2.Core/1.0.3351.48": {"runtime": {"Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.3351.48", "fileVersion": "1.0.3351.48"}}}, "Microsoft.Web.WebView2.WinForms/1.0.3351.48": {"runtime": {"Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.3351.48", "fileVersion": "1.0.3351.48"}}}, "Microsoft.Web.WebView2.Wpf/1.0.3351.48": {"runtime": {"Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.3351.48", "fileVersion": "1.0.3351.48"}}}}}, "libraries": {"YouTubePlayerApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2/1.0.3351.48": {"type": "package", "serviceable": true, "sha512": "sha512-UIzXdl3x3H2MX2es7+BHYT3pLkLoT7090omqla9S3MQQCma0slwiY+CXBM6x0LmdLTpksoQ6XUqOAXrtxjtklQ==", "path": "microsoft.web.webview2/1.0.3351.48", "hashPath": "microsoft.web.webview2.1.0.3351.48.nupkg.sha512"}, "Microsoft.Web.WebView2.Core/1.0.3351.48": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.WinForms/1.0.3351.48": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Wpf/1.0.3351.48": {"type": "reference", "serviceable": false, "sha512": ""}}}