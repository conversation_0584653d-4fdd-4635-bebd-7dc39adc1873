﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FC49A116B794A93132B3E3E83A16538305A93F82"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using YouTubePlayerApp;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartWithWindowsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartFullScreenCheckBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberLastPositionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton YouTubeHomeRadio;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton YouTubeTrendingRadio;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CustomUrlRadio;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomUrlTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VideoQualityComboBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoplayCheckBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MuteOnStartupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VolumeSlider;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ClearCookiesOnExitCheckBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockThirdPartyCookiesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearDataButton;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DoNotTrackCheckBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockAdsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;V1.0.0.0;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StartWithWindowsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.StartFullScreenCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.RememberLastPositionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.YouTubeHomeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 5:
            this.YouTubeTrendingRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 6:
            this.CustomUrlRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 7:
            this.CustomUrlTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.VideoQualityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.AutoplayCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.MuteOnStartupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.VolumeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.ClearCookiesOnExitCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.BlockThirdPartyCookiesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.ClearDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\SettingsWindow.xaml"
            this.ClearDataButton.Click += new System.Windows.RoutedEventHandler(this.ClearDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.DoNotTrackCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.BlockAdsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\SettingsWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

